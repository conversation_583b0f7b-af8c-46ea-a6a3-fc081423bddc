import { useState, useEffect, useCallback } from 'react';
import { User } from '@supabase/supabase-js';
import { authService, AuthUser, AuthState, AuthProvider } from '../lib/auth';

export const useAuth = () => {
  const [state, setState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null
  });

  const setLoading = (loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  };

  const setError = (error: string | null) => {
    setState(prev => ({ ...prev, error }));
  };

  const setUser = (user: AuthUser | null) => {
    setState(prev => ({ ...prev, user, loading: false }));
  };

  const signUp = useCallback(async (email: string, password: string, name?: string) => {
    setLoading(true);
    setError(null);

    try {
      console.log('📝 Starting sign up process...');
      const { data, error } = await authService.signUp(email, password, name);

      if (error) {
        console.error('❌ Sign up failed:', error);
        setError(error);
        setLoading(false);
        return { success: false, error };
      }

      console.log('✅ Supabase sign up successful');

      // 注册成功后获取用户信息，增加重试机制
      if (data?.user) {
        let userInfo = null;
        let retryCount = 0;
        const maxRetries = 3;

        while (!userInfo && retryCount < maxRetries) {
          try {
            console.log(`🔄 Attempting to get user info after signup (attempt ${retryCount + 1}/${maxRetries})`);
            userInfo = await authService.getCurrentUser();

            if (userInfo) {
              console.log('✅ User info retrieved successfully after signup:', userInfo);
              setUser(userInfo);
              break;
            }
          } catch (err) {
            console.warn(`⚠️ Failed to get user info after signup (attempt ${retryCount + 1}):`, err);
          }

          retryCount++;
          if (retryCount < maxRetries) {
            // 等待500ms后重试
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }

        if (!userInfo) {
          console.warn('⚠️ Could not get user info after signup, but registration was successful');
          // 注册成功但无法获取用户信息时，不视为失败
        }
      }

      setLoading(false);
      console.log('🎉 Sign up process completed successfully');
      return { success: true, data };
    } catch (err: any) {
      console.error('❌ Sign up process error:', err);
      setError(err.message || '注册过程中发生错误');
      setLoading(false);
      return { success: false, error: err.message };
    }
  }, []);

  const signIn = useCallback(async (email: string, password: string) => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔐 Starting sign in process...');
      const { data, error } = await authService.signIn(email, password);

      if (error) {
        console.error('❌ Sign in failed:', error);
        setError(error);
        setLoading(false);
        return { success: false, error };
      }

      console.log('✅ Supabase sign in successful, getting user info...');

      // 登录成功后获取用户信息，增加重试机制
      if (data?.user) {
        let userInfo = null;
        let retryCount = 0;
        const maxRetries = 3;

        while (!userInfo && retryCount < maxRetries) {
          try {
            console.log(`🔄 Attempting to get user info (attempt ${retryCount + 1}/${maxRetries})`);
            userInfo = await authService.getCurrentUser();

            if (userInfo && userInfo.email) {
              console.log('✅ User info retrieved successfully:', userInfo);
              setUser(userInfo);
              break;
            } else if (userInfo) {
              console.warn('⚠️ User info incomplete, retrying...', userInfo);
            }
          } catch (err) {
            console.warn(`⚠️ Failed to get user info (attempt ${retryCount + 1}):`, err);
          }

          retryCount++;
          if (retryCount < maxRetries) {
            // 等待500ms后重试
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }

        if (!userInfo) {
          console.error('❌ Failed to get user info after all retries');
          setError('登录成功但无法获取用户信息，请重试');
          setLoading(false);
          return { success: false, error: '无法获取用户信息' };
        }
      }

      setLoading(false);
      console.log('🎉 Sign in process completed successfully');
      return { success: true, data };
    } catch (err: any) {
      console.error('❌ Sign in process error:', err);
      setError(err.message || '登录过程中发生错误');
      setLoading(false);
      return { success: false, error: err.message };
    }
  }, []);

  const signOut = useCallback(async () => {
    setLoading(true);
    const { error } = await authService.signOut();
    
    if (error) {
      setError(error);
      setLoading(false);
      return { success: false, error };
    }
    
    setUser(null);
    return { success: true };
  }, []);

  const signInWithProvider = useCallback(async (provider: AuthProvider) => {
    setLoading(true);
    setError(null);
    
    const { data, error } = await authService.signInWithProvider(provider);
    
    if (error) {
      setError(error);
      setLoading(false);
      return { success: false, error };
    }
    
    // OAuth redirect will handle the rest
    return { success: true, data };
  }, []);

  const updateProfile = useCallback(async (updates: Partial<AuthUser>) => {
    if (!state.user) return { success: false, error: 'No user logged in' };
    
    const { data, error } = await authService.updateProfile(updates);
    
    if (error) {
      setError(error);
      return { success: false, error };
    }
    
    if (data) {
      setUser({ ...state.user, ...updates });
    }
    
    return { success: true, data };
  }, [state.user]);

  const refreshUser = useCallback(async () => {
    const user = await authService.getCurrentUser();
    setUser(user);
  }, []);

  useEffect(() => {
    // Initialize auth state
    const initAuth = async () => {
      const user = await authService.getCurrentUser();
      setUser(user);
    };

    initAuth();

    // Listen for auth changes
    const { data: { subscription } } = authService.onAuthStateChange(async (user: User | null) => {
      if (user) {
        const authUser = await authService.getCurrentUser();
        setUser(authUser);
      } else {
        setUser(null);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return {
    user: state.user,
    loading: state.loading,
    error: state.error,
    signUp,
    signIn,
    signOut,
    signInWithProvider,
    updateProfile,
    refreshUser,
    isAuthenticated: !!state.user
  };
};