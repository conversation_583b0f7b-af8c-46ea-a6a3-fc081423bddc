import type { Context, Next } from 'hono';
import type { Env, Variables } from '../types';
import { SupabaseService } from '../services/supabase';
import { createErrorResponse, ApiErrors } from '../utils/response';

/**
 * 简单的认证中间件
 * 在阶段三会实现完整的 JWT 验证
 */
export async function authMiddleware(
  c: Context<{ Bindings: Env; Variables: Variables }>,
  next: Next
) {
  const context = c.get('context');
  
  // 对于某些端点，认证是可选的
  const path = c.req.path;
  const optionalAuthPaths = ['/v1/test', '/v1/config', '/health', '/v1/credits/packages'];

  if (optionalAuthPaths.some(p => path.includes(p))) {
    await next();
    return;
  }

  // 获取 Authorization header
  const authHeader = c.req.header('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    const apiError = ApiErrors.unauthorized('Authorization header required');
    return createErrorResponse(apiError, context.requestId);
  }

  const token = authHeader.substring(7); // 移除 "Bearer " 前缀
  
  try {
    // 在开发环境中，使用简化的认证逻辑
    if (c.env.ENVIRONMENT === 'development') {
      console.log('🔧 Development mode: checking token:', token);
      // 开发环境：如果 token 是 "dev-user-123"，则认为是有效用户
      if (token === 'dev-user-123') {
        console.log('✅ Development token accepted');
        context.userId = 'dev-user-123';
        await next();
        return;
      }
      console.log('❌ Development token rejected, trying Supabase auth...');
    }

    // 使用 Supabase 验证 JWT token
    const supabaseService = new SupabaseService(c.env);
    const user = await supabaseService.verifyUserAuth(token);

    if (!user) {
      // 在开发环境中，如果Supabase验证也失败，提供更详细的错误信息
      if (c.env.ENVIRONMENT === 'development') {
        console.log('❌ Supabase auth also failed in development mode');
        const apiError = ApiErrors.unauthorized('Authentication failed. Use "dev-user-123" for development or valid Supabase token');
        return createErrorResponse(apiError, context.requestId);
      }

      const apiError = ApiErrors.unauthorized('Invalid or expired token');
      return createErrorResponse(apiError, context.requestId);
    }

    // 将用户的clerk_user_id添加到上下文（这是我们在数据库中使用的ID）
    context.userId = user.clerk_user_id;
    console.log(`✅ Supabase auth successful for user: ${user.clerk_user_id}`);

    await next();
    
  } catch (error) {
    console.error(`[${context.requestId}] Auth middleware error:`, error);
    
    const apiError = ApiErrors.unauthorized('Authentication failed');
    return createErrorResponse(apiError, context.requestId);
  }
}

/**
 * 可选认证中间件
 * 如果有 token 则验证，没有则跳过
 */
export async function optionalAuthMiddleware(
  c: Context<{ Bindings: Env; Variables: Variables }>,
  next: Next
) {
  const context = c.get('context');
  const authHeader = c.req.header('Authorization');
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    
    try {
      // 开发环境简化逻辑
      if (c.env.ENVIRONMENT === 'development' && token === 'dev-user-123') {
        context.userId = 'dev-user-123';
      } else {
        // 验证 Supabase JWT token
        const supabaseService = new SupabaseService(c.env);
        const user = await supabaseService.verifyUserAuth(token);
        if (user) {
          context.userId = user.clerk_user_id;
        }
      }
    } catch (error) {
      console.warn(`[${context.requestId}] Optional auth failed:`, error);
      // 可选认证失败时不阻止请求
    }
  }
  
  await next();
}
