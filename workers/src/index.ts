import { Hono } from 'hono';
import { cors } from 'hono/cors';
import type { Env, RequestContext, Variables } from './types';
import { ConfigService } from './services/config';
import {
  generateRequestId,
  createSuccessResponse,
  createErrorResponse,
  ApiErrors
} from './utils/response';

// 创建 Hono 应用实例
const app = new Hono<{ Bindings: Env; Variables: Variables }>();

// 全局中间件：CORS
app.use('*', cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'], // 本地开发
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'X-Request-ID'],
  exposeHeaders: ['X-Request-ID'],
}));

// 全局中间件：请求上下文
app.use('*', async (c, next) => {
  const requestId = generateRequestId();
  const context: RequestContext = {
    requestId,
    userAgent: c.req.header('User-Agent'),
    ip: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For'),
    timestamp: new Date().toISOString()
  };
  
  // 将上下文添加到请求中
  c.set('context', context);
  
  // 记录请求日志
  console.log(`[${context.requestId}] ${c.req.method} ${c.req.url} - ${context.ip}`);
  
  await next();
});

// 根路径端点
app.get('/', async (c) => {
  const context = c.get('context') as RequestContext;

  const welcomeData = {
    name: 'Eastern Fate Master API',
    version: c.env.API_VERSION || 'v1',
    environment: c.env.ENVIRONMENT || 'development',
    endpoints: {
      health: '/health',
      test: '/v1/test',
      config: '/v1/config (dev only)'
    },
    documentation: 'https://github.com/xianjixiance/React-easternfate/tree/main/workers'
  };

  return createSuccessResponse(welcomeData, context.requestId);
});

// 健康检查端点
app.get('/health', async (c) => {
  const context = c.get('context') as RequestContext;

  try {
    // 检查配置服务
    const configService = new ConfigService(c.env);
    const configs = await configService.getAllConfigs();

    // 检查数据库连接状态
    let databaseStatus = 'unavailable';
    try {
      if (c.env.SUPABASE_URL && c.env.SUPABASE_SERVICE_KEY) {
        databaseStatus = 'healthy';
      } else {
        databaseStatus = 'fallback';
      }
    } catch {
      databaseStatus = 'error';
    }

    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: c.env.API_VERSION || 'v1',
      environment: c.env.ENVIRONMENT || 'development',
      services: {
        config: Object.keys(configs).length > 0 ? 'healthy' : 'fallback',
        database: databaseStatus,
        cache: c.env.CACHE ? 'healthy' : 'unavailable'
      },
      configCount: Object.keys(configs).length
    };

    return createSuccessResponse(healthData, context.requestId);
  } catch (error) {
    console.error(`[${context.requestId}] Health check failed:`, error);

    // 即使出错也返回基础健康信息
    const basicHealthData = {
      status: 'degraded',
      timestamp: new Date().toISOString(),
      version: c.env.API_VERSION || 'v1',
      environment: c.env.ENVIRONMENT || 'development',
      services: {
        config: 'error',
        database: 'error',
        cache: c.env.CACHE ? 'healthy' : 'unavailable'
      },
      error: 'Some services are unavailable'
    };

    return createSuccessResponse(basicHealthData, context.requestId);
  }
});

// 导入路由和中间件
import readingsRouter from './routes/readings';
import creditsRouter from './routes/credits';
import usersRouter from './routes/users';
import { authMiddleware, optionalAuthMiddleware } from './middleware/auth';

// API 版本路由组
const v1 = app.basePath('/v1');

// 先应用认证中间件到需要认证的路由（必须在路由注册之前）
v1.use('/readings/*', authMiddleware);
v1.use('/credits/*', authMiddleware);
// 用户路由中的某些端点不需要认证（如注册），在路由内部处理
v1.use('/users/profile', authMiddleware);
v1.use('/users/stats', authMiddleware);
v1.use('/users/verify-auth', authMiddleware);

// 然后注册路由
v1.route('/readings', readingsRouter);
v1.route('/credits', creditsRouter);
v1.route('/users', usersRouter);

// 测试端点
v1.get('/test', async (c) => {
  const context = c.get('context') as RequestContext;

  const testData = {
    message: 'Eastern Fate Master API is working!',
    requestId: context.requestId,
    timestamp: context.timestamp,
    environment: c.env.ENVIRONMENT || 'development'
  };

  return createSuccessResponse(testData, context.requestId);
});

// 配置信息端点（仅开发环境）
v1.get('/config', async (c) => {
  const context = c.get('context') as RequestContext;
  
  if (c.env.ENVIRONMENT !== 'development') {
    const apiError = ApiErrors.forbidden('Config endpoint only available in development');
    return createErrorResponse(apiError, context.requestId);
  }
  
  try {
    const configService = new ConfigService(c.env);
    const configs = await configService.getAllConfigs();
    
    // 隐藏敏感信息
    const safeConfigs = Object.fromEntries(
      Object.entries(configs).map(([key, value]) => [
        key,
        key.toLowerCase().includes('key') || key.toLowerCase().includes('secret') 
          ? '***hidden***' 
          : value
      ])
    );
    
    return createSuccessResponse(safeConfigs, context.requestId);
  } catch (error) {
    console.error(`[${context.requestId}] Config fetch failed:`, error);
    const apiError = ApiErrors.internalError('Failed to fetch configuration');
    return createErrorResponse(apiError, context.requestId);
  }
});

// 404 处理
app.notFound((c) => {
  const context = c.get('context') as RequestContext;
  const apiError = ApiErrors.notFound(`Endpoint ${c.req.url} not found`);
  return createErrorResponse(apiError, context.requestId);
});

// 全局错误处理
app.onError((error, c) => {
  const context = c.get('context') as RequestContext;
  console.error(`[${context.requestId}] Unhandled error:`, error);
  
  const apiError = ApiErrors.internalError('An unexpected error occurred');
  return createErrorResponse(apiError, context.requestId);
});

// 导出 Workers 处理函数
export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    return app.fetch(request, env, ctx);
  }
};
