import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Env, User, CreditTransaction, AppConfig } from '../types';
import { ConfigService } from './config';

/**
 * Supabase 数据库服务
 * 提供用户管理、积分系统、配置管理等数据库操作
 */
export class SupabaseService {
  public supabase: SupabaseClient; // 改为 public 以便 PromptService 访问
  private configService: ConfigService;

  constructor(env: Env) {
    this.configService = new ConfigService(env);
    
    const supabaseUrl = env.SUPABASE_URL;
    const supabaseKey = env.SUPABASE_SERVICE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration is required');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      db: {
        schema: 'public'
      },
      global: {
        headers: {
          'apikey': supabase<PERSON>ey,
          'Authorization': `Bearer ${supabaseKey}`
        }
      }
    });
  }

  /**
   * 获取用户信息
   */
  async getUser(userId: string): Promise<User | null> {
    try {
      // 在开发环境中，如果是测试用户，直接查询数据库
      const { data, error } = await this.supabase
        .from('users')
        .select('*')
        .eq('clerk_user_id', userId)
        .single();

      if (error) {
        console.error('Failed to get user:', error);
        return null;
      }

      return data as User;
    } catch (error) {
      console.error('Error getting user:', error);
      return null;
    }
  }

  /**
   * 创建或更新用户信息
   */
  async upsertUser(user: Partial<User>): Promise<User | null> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .upsert(user, { onConflict: 'clerk_user_id' })
        .select()
        .single();

      if (error) {
        console.error('Failed to upsert user:', error);
        return null;
      }

      return data as User;
    } catch (error) {
      console.error('Error upserting user:', error);
      return null;
    }
  }

  /**
   * 获取用户积分余额
   */
  async getUserCredits(userId: string): Promise<number> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('credits')
        .eq('clerk_user_id', userId)
        .single();

      if (error) {
        console.error('Failed to get user credits:', error);
        return 0;
      }

      return data?.credits || 0;
    } catch (error) {
      console.error('Error getting user credits:', error);
      return 0;
    }
  }

  /**
   * 消费用户积分
   */
  async consumeCredits(
    userId: string,
    amount: number,
    description: string
  ): Promise<boolean> {
    try {
      // 获取当前积分
      const { data: currentUser, error: getError } = await this.supabase
        .from('users')
        .select('credits')
        .eq('clerk_user_id', userId)
        .single();

      if (getError || !currentUser) {
        console.error('Failed to get current credits:', getError);
        return false;
      }

      if (currentUser.credits < amount) {
        console.warn('Insufficient credits');
        return false;
      }

      // 更新积分余额
      const { error: updateError } = await this.supabase
        .from('users')
        .update({ credits: currentUser.credits - amount })
        .eq('clerk_user_id', userId);

      if (updateError) {
        console.error('Failed to update credits:', updateError);
        return false;
      }

      // 记录交易 - 需要获取用户的实际 UUID
      const user = await this.getUser(userId);
      if (user) {
        await this.createCreditTransaction({
          user_id: user.id, // 使用数据库中的 UUID
          type: 'consume',
          amount: -amount,
          balance_before: currentUser.credits,
          balance_after: currentUser.credits - amount,
          description,
          status: 'completed'
        });
      }

      return true;
    } catch (error) {
      console.error('Error consuming credits:', error);
      return false;
    }
  }

  /**
   * 添加积分
   */
  async addCredits(
    userId: string,
    amount: number,
    type: string,
    description: string,
    reference_id?: string
  ): Promise<boolean> {
    try {
      // 获取当前用户信息
      const { data: currentUser, error: getError } = await this.supabase
        .from('users')
        .select('id, credits')
        .eq('id', userId)
        .single();

      if (getError || !currentUser) {
        console.error('Failed to get current user:', getError);
        return false;
      }

      const newBalance = currentUser.credits + amount;

      // 更新积分余额
      const { error: updateError } = await this.supabase
        .from('users')
        .update({ credits: newBalance })
        .eq('id', userId);

      if (updateError) {
        console.error('Failed to update credits:', updateError);
        return false;
      }

      // 创建交易记录
      await this.createCreditTransaction({
        user_id: userId,
        type: type as any,
        amount: amount,
        balance_before: currentUser.credits,
        balance_after: newBalance,
        description,
        reference_id,
        status: 'completed'
      });

      return true;
    } catch (error) {
      console.error('Error adding credits:', error);
      return false;
    }
  }

  /**
   * 添加积分交易记录（简化版本）
   */
  async addCreditTransaction(
    userId: string,
    amount: number,
    type: string,
    description: string,
    reference_id?: string
  ): Promise<boolean> {
    try {
      // 获取当前余额
      const currentBalance = await this.getUserCredits(userId);

      await this.createCreditTransaction({
        user_id: userId,
        type: type as any,
        amount: amount,
        balance_before: currentBalance,
        balance_after: currentBalance + amount,
        description,
        reference_id,
        status: 'completed'
      });

      return true;
    } catch (error) {
      console.error('Error adding credit transaction:', error);
      return false;
    }
  }

  /**
   * 创建积分交易记录
   */
  async createCreditTransaction(
    transaction: Omit<CreditTransaction, 'id' | 'created_at'>
  ): Promise<CreditTransaction | null> {
    try {
      const { data, error } = await this.supabase
        .from('credit_transactions')
        .insert(transaction)
        .select()
        .single();

      if (error) {
        console.error('Failed to create credit transaction:', error);
        return null;
      }

      return data as CreditTransaction;
    } catch (error) {
      console.error('Error creating credit transaction:', error);
      return null;
    }
  }

  /**
   * 获取用户积分交易历史
   */
  async getCreditTransactions(
    userId: string,
    limit: number = 50
  ): Promise<CreditTransaction[]> {
    try {
      // 先获取用户的实际 UUID
      const user = await this.getUser(userId);
      if (!user) {
        return [];
      }

      const { data, error } = await this.supabase
        .from('credit_transactions')
        .select('*')
        .eq('user_id', user.id) // 使用数据库中的 UUID
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Failed to get credit transactions:', error);
        return [];
      }

      return data as CreditTransaction[];
    } catch (error) {
      console.error('Error getting credit transactions:', error);
      return [];
    }
  }

  /**
   * 获取积分套餐
   */
  async getCreditPackages(): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('credit_packages')
        .select('*')
        .eq('is_active', true)
        .order('credits', { ascending: true });

      if (error) {
        console.error('Failed to get credit packages:', error);
        // 返回默认套餐作为降级方案
        return this.getDefaultCreditPackages();
      }

      return data || this.getDefaultCreditPackages();
    } catch (error) {
      console.error('Error getting credit packages:', error);
      return this.getDefaultCreditPackages();
    }
  }

  /**
   * 获取默认积分套餐（降级方案）
   */
  private getDefaultCreditPackages(): any[] {
    return [
      {
        id: 'starter',
        name: 'Starter Package',
        name_zh: '入门套餐',
        credits: 50,
        price: 9.99,
        currency: 'USD',
        popular: false,
        features: ['5 detailed readings', 'Basic crystal recommendations', 'Email support'],
        features_zh: ['5次详细解读', '基础水晶推荐', '邮件支持'],
        is_active: true
      },
      {
        id: 'professional',
        name: 'Professional Package',
        name_zh: '专业套餐',
        credits: 150,
        price: 24.99,
        currency: 'USD',
        popular: true,
        features: ['15 detailed readings', 'Premium crystal recommendations', 'Priority support', 'Monthly horoscope'],
        features_zh: ['15次详细解读', '高级水晶推荐', '优先支持', '月度运势'],
        is_active: true
      },
      {
        id: 'master',
        name: 'Master Package',
        name_zh: '大师套餐',
        credits: 300,
        price: 49.99,
        currency: 'USD',
        popular: false,
        features: ['30 detailed readings', 'Exclusive crystal recommendations', '24/7 priority support', 'Weekly horoscope', 'Personal consultation'],
        features_zh: ['30次详细解读', '独家水晶推荐', '24/7优先支持', '周度运势', '个人咨询'],
        is_active: true
      }
    ];
  }

  /**
   * 验证用户认证状态
   */
  async verifyUserAuth(token: string): Promise<User | null> {
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser(token);

      if (error || !user) {
        console.error('Failed to verify user auth:', error);
        return null;
      }

      // 获取用户详细信息，使用 Supabase user.id 作为 clerk_user_id
      let dbUser = await this.getUser(user.id);

      if (!dbUser) {
        // 如果数据库中没有用户记录，自动创建用户
        console.log('User not found in database, creating new user record');

        const newUserData = {
          clerk_user_id: user.id,
          email: user.email || '',
          name: user.user_metadata?.name || user.email?.split('@')[0] || '',
          avatar_url: user.user_metadata?.avatar_url,
          credits: 10, // 新用户赠送10积分
          language_preference: 'zh',
          timezone: 'UTC',
          subscription_status: 'free',
          total_readings: 0,
          last_reading_at: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_active: true
        };

        dbUser = await this.upsertUser(newUserData);

        if (!dbUser) {
          console.error('Failed to create user record in database');
          // 返回基本用户对象作为fallback
          return {
            id: '', // 数据库ID为空
            clerk_user_id: user.id,
            email: user.email || '',
            name: user.user_metadata?.name || '',
            avatar_url: user.user_metadata?.avatar_url,
            credits: 0,
            language_preference: 'zh',
            timezone: 'UTC',
            subscription_status: 'free',
            total_readings: 0,
            last_reading_at: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            is_active: true
          } as User;
        }

        // 为新用户添加欢迎积分交易记录
        if (dbUser.id) {
          try {
            await this.addCreditTransaction(
              dbUser.id,
              10,
              'bonus',
              'Welcome bonus for new user'
            );
            console.log('Welcome bonus added for new user');
          } catch (error) {
            console.warn('Failed to add welcome bonus:', error);
          }
        }

        console.log('New user created successfully:', dbUser.id);
      }

      return dbUser;
    } catch (error) {
      console.error('Error verifying user auth:', error);
      return null;
    }
  }

  /**
   * 存储八字分析结果
   */
  async saveReading(reading: {
    user_id: string;
    birth_date: string;
    birth_time: string;
    birth_location: any;
    timezone: string;
    gender: string;
    result: any;
    summary: string;
  }): Promise<string | null> {
    try {
      const { data, error } = await this.supabase
        .from('readings')
        .insert({
          user_id: reading.user_id,
          type: 'bazi',
          status: 'completed',
          birth_date: reading.birth_date,
          birth_time: reading.birth_time,
          birth_location: reading.birth_location,
          timezone: reading.timezone,
          gender: reading.gender,
          result: reading.result,
          summary: reading.summary,
          language: 'zh',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (error) {
        console.error('Failed to save reading:', error);
        return null;
      }

      return data.id;
    } catch (error) {
      console.error('Error saving reading:', error);
      return null;
    }
  }

  /**
   * 获取八字分析结果
   */
  async getReading(readingId: string, userId?: string): Promise<any | null> {
    try {
      let query = this.supabase
        .from('readings')
        .select('*')
        .eq('id', readingId);

      // 如果提供了用户ID，确保只能访问自己的记录
      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query.single();

      if (error) {
        console.error('Failed to get reading:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error getting reading:', error);
      return null;
    }
  }

  /**
   * 获取用户的八字分析历史
   */
  async getUserReadings(userId: string, limit: number = 20): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('readings')
        .select('id, created_at, birth_date, birth_time, birth_location, result')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Failed to get user readings:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error getting user readings:', error);
      return [];
    }
  }

  /**
   * 获取应用配置
   */
  async getAppConfig(key: string): Promise<string | null> {
    return await this.configService.getConfig(key);
  }

  /**
   * 获取所有应用配置
   */
  async getAllAppConfigs(): Promise<Record<string, string>> {
    return await this.configService.getAllConfigs();
  }
}
